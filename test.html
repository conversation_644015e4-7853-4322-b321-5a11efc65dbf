<section class="services-section-commandnet">
  <span class="services-subtitle-commandnet">what we're offering <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="arrow-icon-commandnet">
      <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3" />
    </svg>
  </span>
  <h1 class="services-title-commandnet">Services Built Specifically for your Business</h1>
  <div class="grid-offer-commandnet">
    <div class="card-commandnet">
      <div class="circle-commandnet">
      </div>
      <div class="card-content-commandnet card-content-right-commandnet">
        <h2 class="card-title-commandnet">uI/uX <br /> creative design</h2>
        <p class="card-description-commandnet">Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.</p>
      </div>
      <div class="icon-commandnet"></div>
    </div>
    <div class="card-commandnet">
      <div class="circle-commandnet">
      </div>
      <div class="card-content-commandnet card-content-left-commandnet">
        <h2 class="card-title-commandnet">visual <br /> graphic design</h2>
        <p class="card-description-commandnet">Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.</p>
      </div>
    </div>
    <div class="card-commandnet">
      <div class="circle-commandnet">
      </div>
      <div class="card-content-commandnet card-content-right-commandnet">
        <h2 class="card-title-commandnet">strategy & <br />digital marketing</h2>
        <p class="card-description-commandnet">Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.</p>
      </div>
    </div>
    <div class="card-commandnet">
      <div class="circle-commandnet">
      </div>
      <div class="card-content-commandnet card-content-left-commandnet">
        <h2 class="card-title-commandnet">effective<br /> business growth</h2>
        <p class="card-description-commandnet">Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Pellentesque habitant morbi tristique senectus et netus et malesuada fames.</p>
      </div>
    </div>
  </div>
  <style>
    @import url("https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;1,400&display=swap");

    /* Reset and base styles */
    * {
      box-sizing: border-box;
    }

    /* Services section - replaces Tailwind classes */
    .services-section-commandnet {
      min-height: 100vh;
      background-color: #111827; /* bg-gray-900 */
      text-align: center;
      padding: 5rem 2rem; /* py-20 px-8 */
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    /* Services subtitle - replaces Tailwind classes */
    .services-subtitle-commandnet {
      color: #9ca3af; /* text-gray-400 */
      font-size: 1.125rem; /* text-lg */
      max-width: 32rem; /* max-w-lg */
      margin: 0 auto 0.5rem auto; /* mx-auto mb-2 */
      text-transform: capitalize;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    /* Arrow icon - replaces Tailwind classes */
    .arrow-icon-commandnet {
      color: #4f46e5; /* text-indigo-600 */
      margin-left: 0.75rem; /* ml-3 */
      width: 1.5rem; /* w-6 */
      height: 1.5rem; /* h-6 */
    }

    /* Services title - replaces Tailwind classes */
    .services-title-commandnet {
      color: white; /* text-white */
      font-size: 2.25rem; /* text-4xl */
      font-weight: 600; /* font-semibold */
      max-width: 48rem; /* max-w-3xl */
      margin: 0 auto 4rem auto; /* mx-auto mb-16 */
      line-height: 1.375; /* leading-snug */
    }

    /* Grid container - replaces Tailwind classes */
    .grid-offer-commandnet {
      text-align: left;
      display: grid;
      gap: 1.25rem; /* gap-5 */
      max-width: 80rem; /* max-w-5xl */
      margin: 0 auto;
      grid-template-columns: 1fr;
    }

    /* Card base styles - replaces Tailwind classes */
    .card-commandnet {
      background-color: #1f2937; /* bg-gray-800 */
      padding: 2.5rem; /* p-10 */
      position: relative;
    }

    /* Card content positioning */
    .card-content-commandnet {
      position: relative;
      z-index: 1;
    }

    .card-content-right-commandnet {
      padding-right: 0;
    }

    .card-content-left-commandnet {
      padding-left: 0;
    }

    /* Card title - replaces Tailwind classes */
    .card-title-commandnet {
      text-transform: capitalize;
      color: white; /* text-white */
      margin-bottom: 1rem; /* mb-4 */
      font-size: 1.5rem; /* text-2xl */
      font-family: "Playfair Display", serif;
      font-optical-sizing: auto;
      font-weight: 400;
      font-style: normal;
    }

    /* Card description - replaces Tailwind classes */
    .card-description-commandnet {
      color: #9ca3af; /* text-gray-400 */
      transition: 0.8s;
    }

    /* Responsive design */
    @media (min-width: 640px) {
      .grid-offer-commandnet {
        grid-template-columns: repeat(2, 1fr); /* sm:grid-cols-2 */
      }
    }

    @media (min-width: 768px) {
      .services-title-commandnet {
        font-size: 3rem; /* md:text-5xl */
      }
    }

    @media (min-width: 1024px) {
      .card-content-right-commandnet {
        padding-right: 13rem; /* lg:pr-52 */
      }

      .card-content-left-commandnet {
        padding-left: 12rem; /* lg:pl-48 */
      }

      .card-commandnet:nth-child(3) .card-content-right-commandnet {
        padding-right: 11rem; /* lg:pr-44 */
      }
    }

    @media (min-width: 1280px) {
      .services-section-commandnet {
        padding-left: 0; /* xl:px-0 */
        padding-right: 0;
      }

      .services-title-commandnet {
        font-size: 3.75rem; /* xl:text-6xl */
      }

      .card-title-commandnet {
        font-size: 1.875rem; /* xl:text-3xl */
      }
    }

    /* Existing card hover effects and animations */
    .card-commandnet::before {
      position: absolute;
      content: "";
      width: 100%;
      height: 100%;
      transition: 0.6s;
      z-index: 0;
      background-color: #4f46e5;
    }

    .card-commandnet:hover {
      box-shadow: 0.063rem 0.063rem 1.25rem 0.375rem rgba(0, 0, 0, 0.53);
    }

    .card-commandnet:nth-child(1)::before {
      bottom: 0;
      right: 0;
      clip-path: circle(calc(6.25rem + 7.5vw) at 100% 100%);
    }

    .card-commandnet:nth-child(2)::before {
      bottom: 0;
      left: 0;
      clip-path: circle(calc(6.25rem + 7.5vw) at 0% 100%);
    }

    .card-commandnet:nth-child(3)::before {
      top: 0;
      right: 0;
      clip-path: circle(calc(6.25rem + 7.5vw) at 100% 0%);
    }

    .card-commandnet:nth-child(4)::before {
      top: 0;
      left: 0;
      clip-path: circle(calc(6.25rem + 7.5vw) at 0% 0%);
    }

    .card-commandnet:hover::before {
      clip-path: circle(110vw at 100% 100%);
    }

    .card-commandnet:hover .card-description-commandnet {
      color: #fff;
    }

    @media screen and (min-width: 62.5rem) {
      .circle-commandnet {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 0;
      }
    }

    .card-commandnet:nth-child(1) .circle-commandnet {
      background: url("https://images.unsplash.com/photo-1587440871875-191322ee64b0?q=80&w=2071&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
      bottom: 0;
      right: 0;
      clip-path: circle(calc(6.25rem + 7.5vw) at 100% 100%);
    }

    .card-commandnet:nth-child(2) .circle-commandnet {
      background: url("https://images.unsplash.com/photo-1499951360447-b19be8fe80f5?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
      bottom: 0;
      left: 0;
      clip-path: circle(calc(6.25rem + 7.5vw) at 0% 100%);
    }

    .card-commandnet:nth-child(3) .circle-commandnet {
      background: url("https://images.unsplash.com/photo-1557804506-669a67965ba0?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
      top: 0;
      right: 0;
      clip-path: circle(calc(6.25rem + 7.5vw) at 100% 0%);
    }

    .card-commandnet:nth-child(4) .circle-commandnet {
      background: url("https://images.unsplash.com/photo-1600880292203-757bb62b4baf?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") no-repeat 50% 50%/cover;
      top: 0;
      left: 0;
      clip-path: circle(calc(6.25rem + 7.5vw) at 0% 0%);
    }
  </style>
</section>